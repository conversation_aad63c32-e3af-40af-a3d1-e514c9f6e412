/* Enhanced Skills Container */
.parent__container {
  z-index: 89;
  position: absolute;
  width: 80%;
  height: 80%;
  animation: backgroundAnimation 10s ease infinite;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.enhanced-skills-container {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

.parent__container:hover canvas {
  transform: scale(1.02);
  cursor: pointer;
}

canvas {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 15px;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
}

/* Skills Header */
.skills-header {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  z-index: 10;
}

.skills-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--color-primary);
  margin: 0 0 5px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.skills-subtitle {
  font-size: 0.85rem;
  color: var(--color-secondary-text);
  margin: 0;
  opacity: 0.8;
}

/* Enhanced Tooltip */
.tooltip {
  position: absolute;
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.8));
  color: #fff;
  border-radius: 12px;
  pointer-events: none;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 200px;
  z-index: 1000;
}

.enhanced-tooltip {
  font-size: 0.85rem;
  line-height: 1.4;
}

.tooltip-header {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.tooltip-category {
  font-size: 0.7rem;
  opacity: 0.7;
  color: var(--color-primary);
}

.tooltip-proficiency {
  color: #ffd700;
  font-size: 0.9rem;
  margin-bottom: 6px;
}

.tooltip-tip {
  font-size: 0.7rem;
  opacity: 0.6;
  font-style: italic;
}

/* Enhanced Theme Selector */
.theme-selector {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.enhanced-theme-selector {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.theme-label {
  font-size: 0.75rem;
  color: var(--color-secondary-text);
  font-weight: 500;
  margin-right: 4px;
}

.theme-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.theme-button:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.theme-button:active {
  transform: scale(0.95);
}

/* Enhanced Details Panel */
.details {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%) scale(0.9);
  padding: 16px 20px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.8));
  color: #fff;
  border-radius: 16px;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  max-width: 300px;
  min-width: 250px;
  z-index: 1000;
}

.enhanced-details {
  font-size: 0.9rem;
  line-height: 1.5;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.details-header h4 {
  margin: 0;
  color: var(--color-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.details-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.details-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.details-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.details-btn {
  flex: 1;
  padding: 8px 12px;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.details-btn:hover {
  background: var(--color-primary-rgb);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(84, 98, 255, 0.3);
}

/* Controls Panel */
.controls-panel {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.control-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 6px;
  border-radius: 8px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.control-btn:active {
  transform: scale(0.95);
}

.speed-control {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.75rem;
  color: var(--color-secondary-text);
}

.speed-control label {
  font-weight: 500;
}

.speed-control input[type="range"] {
  width: 60px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
}

.speed-control input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  background: var(--color-primary);
  border-radius: 50%;
  cursor: pointer;
}

.speed-control input[type="range"]::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: var(--color-primary);
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

/* Skill Counter */
.skill-counter {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-size: 0.85rem;
  color: var(--color-secondary-text);
  font-weight: 500;
}

.skill-counter span {
  color: var(--color-primary);
  font-weight: 600;
  font-size: 1rem;
}

/* Skill Stats Container */
.skill-stats-container {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  flex-wrap: wrap;
}

.skill-stat-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1rem 1.25rem;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 120px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.skill-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.stat-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  display: block;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: 0.25rem;
  line-height: 1;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--color-secondary-text);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Animations */
@keyframes skillPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes skillGlow {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(84, 98, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(84, 98, 255, 0.6);
  }
}

.skill-stat-card:hover {
  animation: skillPulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .parent__container {
    width: 95%;
    height: 85%;
  }

  .skills-header {
    top: 10px;
  }

  .skills-title {
    font-size: 1rem;
  }

  .skills-subtitle {
    font-size: 0.75rem;
  }

  .theme-selector {
    top: 10px;
    right: 10px;
    padding: 6px 8px;
    gap: 6px;
  }

  .theme-button {
    width: 20px;
    height: 20px;
  }

  .theme-label {
    display: none;
  }

  .controls-panel {
    bottom: 10px;
    right: 10px;
    padding: 6px 8px;
    gap: 8px;
  }

  .control-btn {
    font-size: 1rem;
    padding: 4px;
  }

  .speed-control {
    font-size: 0.7rem;
  }

  .speed-control input[type="range"] {
    width: 50px;
  }

  .skill-counter {
    top: 10px;
    left: 10px;
    padding: 6px 10px;
    font-size: 0.75rem;
  }

  .skill-counter span {
    font-size: 0.9rem;
  }

  .details {
    bottom: 10px;
    max-width: 280px;
    min-width: 220px;
    padding: 12px 16px;
  }

  .details-header h4 {
    font-size: 1rem;
  }

  .tooltip {
    max-width: 180px;
    padding: 10px 12px;
    font-size: 0.8rem;
  }

  .skill-stats-container {
    gap: 0.75rem;
    margin-top: 1rem;
  }

  .skill-stat-card {
    min-width: 100px;
    padding: 0.75rem 1rem;
  }

  .stat-icon {
    font-size: 1.25rem;
  }

  .stat-value {
    font-size: 1.25rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .skill-stats-container {
    flex-direction: column;
    align-items: center;
  }

  .skill-stat-card {
    width: 100%;
    max-width: 200px;
  }

  .controls-panel {
    flex-direction: column;
    gap: 6px;
    padding: 8px;
  }

  .speed-control {
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }

  .details {
    max-width: 260px;
    min-width: 200px;
  }

  .details-actions {
    flex-direction: column;
    gap: 6px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .parent__container {
    border: 2px solid var(--color-primary);
    background: rgba(255, 255, 255, 0.1);
  }

  .tooltip,
  .details {
    background: rgba(0, 0, 0, 0.95);
    border: 2px solid var(--color-primary);
  }

  .theme-button {
    border-width: 3px;
  }

  .skill-stat-card {
    border: 2px solid rgba(255, 255, 255, 0.3);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .parent__container,
  .canvas,
  .tooltip,
  .details,
  .theme-button,
  .control-btn,
  .skill-stat-card {
    transition: none;
    animation: none;
  }

  .parent__container:hover canvas {
    transform: none;
  }
}
