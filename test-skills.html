<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Skills Section Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .hero {
            display: flex;
            gap: 2rem;
            align-items: center;
            min-height: 600px;
        }
        
        .hero__content {
            flex: 1;
            color: white;
        }
        
        .hero__img {
            flex: 1;
            position: relative;
            height: 500px;
        }
        
        .main__title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: white;
        }
        
        .sub__title {
            font-size: 1.1rem;
            line-height: 1.6;
            opacity: 0.9;
            margin-bottom: 1.5rem;
        }
        
        /* Import enhanced skills styles */
        :root {
            --color-primary: #5462ffe4;
            --color-primary-rgb: 84, 98, 255;
            --color-secondary: #fff3f1;
            --color-secondary-dark: #3a3a3afc;
            --color-light: #fff;
            --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            --radius: 0.75rem;
            --color-secondary-text: #6b7280;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 Enhanced Skills Section</h1>
        <div id="skills-test"></div>
    </div>

    <script type="module">
        // Mock the required utilities and variables
        const createHtmlElement = (tag, attributes = {}) => {
            const element = document.createElement(tag);
            Object.entries(attributes).forEach(([key, value]) => {
                if (key === 'class') {
                    element.className = value;
                } else if (key === 'style') {
                    element.style.cssText = value;
                } else {
                    element.setAttribute(key, value);
                }
            });
            return element;
        };

        const renderTitle = (titles, container) => {
            titles.forEach((title, index) => {
                const span = document.createElement('span');
                span.textContent = title;
                if (index > 0) span.style.color = 'var(--color-primary)';
                container.appendChild(span);
                if (index < titles.length - 1) {
                    container.appendChild(document.createTextNode(' '));
                }
            });
        };

        const renderSubTitle = (text, className) => {
            const element = createHtmlElement('p', { class: className });
            element.textContent = text;
            return element;
        };

        const alertBadge = (text, color) => {
            console.log(`Badge: ${text} (${color})`);
        };

        const skillText = "Master of Modern Web Development: Crafting dynamic, visually stunning interfaces with a strong foundation in HTML, CSS, and JavaScript. Proficient in advanced technologies like React and TypeScript, with a knack for efficient styling using SCSS, Bootstrap, and Tailwind. Skilled in seamless version control with Git.";

        const items = [
            "JavaScript", "TypeScript", "HTML5", "CSS3", "React", "Webpack",
            "JQuery", "Git", "BEM", "SASS", "Next JS", "Node JS", "Vue JS"
        ];

        // Test the enhanced skills section
        async function testSkillsSection() {
            try {
                // Load the skills CSS
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'src/style/skills.css';
                document.head.appendChild(link);

                // Import and create the skills section
                const { default: skillSection } = await import('./src/sections/skill.js');
                const skillsElement = skillSection();
                skillsElement.style.display = 'block';
                
                document.getElementById('skills-test').appendChild(skillsElement);
                
                console.log('✅ Enhanced skills section loaded successfully!');
                console.log('Features included:');
                console.log('- Interactive skill bubbles with hover effects');
                console.log('- Enhanced tooltips with skill categories and proficiency');
                console.log('- Detailed skill information panels');
                console.log('- Multiple theme options');
                console.log('- Animation controls (play/pause, speed)');
                console.log('- Skill statistics cards');
                console.log('- Mobile-responsive design');
                console.log('- Accessibility improvements');
                
            } catch (error) {
                console.error('❌ Error loading skills section:', error);
                document.getElementById('skills-test').innerHTML = `
                    <div style="color: red; text-align: center; padding: 2rem;">
                        <h2>Error Loading Skills Section</h2>
                        <p>Please make sure all files are properly set up.</p>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }

        // Initialize the test
        testSkillsSection();
    </script>
</body>
</html>
